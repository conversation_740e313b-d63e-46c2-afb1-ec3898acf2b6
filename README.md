# ICE Miner Tycoon

A new Flutter project.

## Structuring the Project: Feature-First for Clarity

lib/
└── src/
    ├── features/
    │   ├── game/      # Core gameplay logic, state, and UI
    │   ├── shop/      # Generators and upgrades store
    │   └── settings/  # App settings, data reset functionality
    ├── core/
    │   ├── persistence/ # Abstraction for data storage (Hive)
    │   ├── routing/     # Navigation configuration (GoRouter)
    │   └── utils/       # Shared utilities, formatters, etc.
    └── app.dart         # Root widget, MaterialApp setup
└── main.dart            # App entry point

Each feature directory will contain three subdirectories representing these layers:   
- presentation: Contains the UI code (Widgets) and any UI-specific state controllers (like AnimationController). This layer is responsible for what the user sees.
- application: Contains the core business logic. In our Riverpod architecture, this is where the Notifier classes will reside. This layer orchestrates the flow of data and handles user interactions.
- data: Contains the data access logic. This includes Repository implementations and data models (freezed classes). This layer is responsible for where the data comes from.

## Storage

The hive and hive_generator packages are no longer actively maintained; therefore, we will use hive_ce (Community Edition), the actively maintained successor, to ensure long-term project viability