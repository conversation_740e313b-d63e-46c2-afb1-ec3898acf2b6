import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive_ce/hive.dart';
import 'generator_model.dart';

part 'game_state_model.freezed.dart';
part 'game_state_model.g.dart';

@freezed
@HiveType(typeId: 0)
abstract class GameState with _$GameState {
  const factory GameState({
    @HiveField(0) BigInt iceShards,
    @HiveField(1) required List<Generator> generators,
    @HiveField(2) DateTime? lastUpdated,
  }) = _GameState;

  factory GameState.fromJson(Map<String, dynamic> json) => _$GameStateFromJson(json);
}