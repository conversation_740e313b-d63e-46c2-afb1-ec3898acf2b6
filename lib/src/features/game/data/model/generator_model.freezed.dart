// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'generator_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Generator {

@HiveField(0) String get id;@HiveField(1) String get name;@HiveField(2) String get description;@HiveField(3) BigInt get baseCost;@HiveField(4) double get costMultiplier;@HiveField(5) BigInt get baseProduction;@HiveField(6) int get level;
/// Create a copy of Generator
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GeneratorCopyWith<Generator> get copyWith => _$GeneratorCopyWithImpl<Generator>(this as Generator, _$identity);

  /// Serializes this Generator to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Generator&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.baseCost, baseCost) || other.baseCost == baseCost)&&(identical(other.costMultiplier, costMultiplier) || other.costMultiplier == costMultiplier)&&(identical(other.baseProduction, baseProduction) || other.baseProduction == baseProduction)&&(identical(other.level, level) || other.level == level));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,baseCost,costMultiplier,baseProduction,level);

@override
String toString() {
  return 'Generator(id: $id, name: $name, description: $description, baseCost: $baseCost, costMultiplier: $costMultiplier, baseProduction: $baseProduction, level: $level)';
}


}

/// @nodoc
abstract mixin class $GeneratorCopyWith<$Res>  {
  factory $GeneratorCopyWith(Generator value, $Res Function(Generator) _then) = _$GeneratorCopyWithImpl;
@useResult
$Res call({
@HiveField(0) String id,@HiveField(1) String name,@HiveField(2) String description,@HiveField(3) BigInt baseCost,@HiveField(4) double costMultiplier,@HiveField(5) BigInt baseProduction,@HiveField(6) int level
});




}
/// @nodoc
class _$GeneratorCopyWithImpl<$Res>
    implements $GeneratorCopyWith<$Res> {
  _$GeneratorCopyWithImpl(this._self, this._then);

  final Generator _self;
  final $Res Function(Generator) _then;

/// Create a copy of Generator
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? description = null,Object? baseCost = null,Object? costMultiplier = null,Object? baseProduction = null,Object? level = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,baseCost: null == baseCost ? _self.baseCost : baseCost // ignore: cast_nullable_to_non_nullable
as BigInt,costMultiplier: null == costMultiplier ? _self.costMultiplier : costMultiplier // ignore: cast_nullable_to_non_nullable
as double,baseProduction: null == baseProduction ? _self.baseProduction : baseProduction // ignore: cast_nullable_to_non_nullable
as BigInt,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [Generator].
extension GeneratorPatterns on Generator {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Generator value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Generator() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Generator value)  $default,){
final _that = this;
switch (_that) {
case _Generator():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Generator value)?  $default,){
final _that = this;
switch (_that) {
case _Generator() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@HiveField(0)  String id, @HiveField(1)  String name, @HiveField(2)  String description, @HiveField(3)  BigInt baseCost, @HiveField(4)  double costMultiplier, @HiveField(5)  BigInt baseProduction, @HiveField(6)  int level)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Generator() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.baseCost,_that.costMultiplier,_that.baseProduction,_that.level);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@HiveField(0)  String id, @HiveField(1)  String name, @HiveField(2)  String description, @HiveField(3)  BigInt baseCost, @HiveField(4)  double costMultiplier, @HiveField(5)  BigInt baseProduction, @HiveField(6)  int level)  $default,) {final _that = this;
switch (_that) {
case _Generator():
return $default(_that.id,_that.name,_that.description,_that.baseCost,_that.costMultiplier,_that.baseProduction,_that.level);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@HiveField(0)  String id, @HiveField(1)  String name, @HiveField(2)  String description, @HiveField(3)  BigInt baseCost, @HiveField(4)  double costMultiplier, @HiveField(5)  BigInt baseProduction, @HiveField(6)  int level)?  $default,) {final _that = this;
switch (_that) {
case _Generator() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.baseCost,_that.costMultiplier,_that.baseProduction,_that.level);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Generator implements Generator {
  const _Generator({@HiveField(0) required this.id, @HiveField(1) required this.name, @HiveField(2) required this.description, @HiveField(3) required this.baseCost, @HiveField(4) required this.costMultiplier, @HiveField(5) required this.baseProduction, @HiveField(6) this.level = 0});
  factory _Generator.fromJson(Map<String, dynamic> json) => _$GeneratorFromJson(json);

@override@HiveField(0) final  String id;
@override@HiveField(1) final  String name;
@override@HiveField(2) final  String description;
@override@HiveField(3) final  BigInt baseCost;
@override@HiveField(4) final  double costMultiplier;
@override@HiveField(5) final  BigInt baseProduction;
@override@JsonKey()@HiveField(6) final  int level;

/// Create a copy of Generator
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GeneratorCopyWith<_Generator> get copyWith => __$GeneratorCopyWithImpl<_Generator>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GeneratorToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Generator&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.baseCost, baseCost) || other.baseCost == baseCost)&&(identical(other.costMultiplier, costMultiplier) || other.costMultiplier == costMultiplier)&&(identical(other.baseProduction, baseProduction) || other.baseProduction == baseProduction)&&(identical(other.level, level) || other.level == level));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,baseCost,costMultiplier,baseProduction,level);

@override
String toString() {
  return 'Generator(id: $id, name: $name, description: $description, baseCost: $baseCost, costMultiplier: $costMultiplier, baseProduction: $baseProduction, level: $level)';
}


}

/// @nodoc
abstract mixin class _$GeneratorCopyWith<$Res> implements $GeneratorCopyWith<$Res> {
  factory _$GeneratorCopyWith(_Generator value, $Res Function(_Generator) _then) = __$GeneratorCopyWithImpl;
@override @useResult
$Res call({
@HiveField(0) String id,@HiveField(1) String name,@HiveField(2) String description,@HiveField(3) BigInt baseCost,@HiveField(4) double costMultiplier,@HiveField(5) BigInt baseProduction,@HiveField(6) int level
});




}
/// @nodoc
class __$GeneratorCopyWithImpl<$Res>
    implements _$GeneratorCopyWith<$Res> {
  __$GeneratorCopyWithImpl(this._self, this._then);

  final _Generator _self;
  final $Res Function(_Generator) _then;

/// Create a copy of Generator
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? description = null,Object? baseCost = null,Object? costMultiplier = null,Object? baseProduction = null,Object? level = null,}) {
  return _then(_Generator(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,baseCost: null == baseCost ? _self.baseCost : baseCost // ignore: cast_nullable_to_non_nullable
as BigInt,costMultiplier: null == costMultiplier ? _self.costMultiplier : costMultiplier // ignore: cast_nullable_to_non_nullable
as double,baseProduction: null == baseProduction ? _self.baseProduction : baseProduction // ignore: cast_nullable_to_non_nullable
as BigInt,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
