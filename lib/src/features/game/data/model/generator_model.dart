import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive_ce/hive.dart';

part 'generator_model.freezed.dart';
part 'generator_model.g.dart';

@freezed
@HiveType(typeId: 1)
abstract class Generator with _$Generator {
  const factory Generator({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String description,
    @HiveField(3) required BigInt baseCost,
    @HiveField(4) required double costMultiplier,
    @HiveField(5) required BigInt baseProduction,
    @HiveField(6) @Default(0) int level,
  }) = _Generator;

  factory Generator.fromJson(Map<String, dynamic> json) => _$GeneratorFromJson(json);
}
