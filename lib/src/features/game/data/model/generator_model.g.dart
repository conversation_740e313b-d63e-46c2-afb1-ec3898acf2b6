// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'generator_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GeneratorAdapter extends TypeAdapter<Generator> {
  @override
  final typeId = 1;

  @override
  Generator read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Generator(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      baseCost: fields[3] as BigInt,
      costMultiplier: (fields[4] as num).toDouble(),
      baseProduction: fields[5] as BigInt,
      level: fields[6] == null ? 0 : (fields[6] as num).toInt(),
    );
  }

  @override
  void write(BinaryWriter writer, Generator obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.baseCost)
      ..writeByte(4)
      ..write(obj.costMultiplier)
      ..writeByte(5)
      ..write(obj.baseProduction)
      ..writeByte(6)
      ..write(obj.level);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GeneratorAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Generator _$GeneratorFromJson(Map<String, dynamic> json) => _Generator(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  baseCost: BigInt.parse(json['baseCost'] as String),
  costMultiplier: (json['costMultiplier'] as num).toDouble(),
  baseProduction: BigInt.parse(json['baseProduction'] as String),
  level: (json['level'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$GeneratorToJson(_Generator instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'baseCost': instance.baseCost.toString(),
      'costMultiplier': instance.costMultiplier,
      'baseProduction': instance.baseProduction.toString(),
      'level': instance.level,
    };
