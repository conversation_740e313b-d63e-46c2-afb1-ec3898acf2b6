// Generated by Hive CE
// Do not modify
// Check in to version control

import 'package:hive_ce/hive.dart';
import 'package:ice_miner_tycoon/src/features/game/data/model/game_state_model.dart';
import 'package:ice_miner_tycoon/src/features/game/data/model/generator_model.dart';

extension HiveRegistrar on HiveInterface {
  void registerAdapters() {
    registerAdapter(GameStateAdapter());
    registerAdapter(GeneratorAdapter());
  }
}

extension IsolatedHiveRegistrar on IsolatedHiveInterface {
  void registerAdapters() {
    registerAdapter(GameStateAdapter());
    registerAdapter(GeneratorAdapter());
  }
}
